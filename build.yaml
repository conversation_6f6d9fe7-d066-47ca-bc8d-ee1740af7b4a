targets:
  $default:
    builders:
      # Riverpod代码生成
      riverpod_generator:riverpod_generator:
        enabled: true
        options:
          # 生成的文件后缀
          suffix: ".g.dart"
      
      # Freezed代码生成
      freezed:freezed:
        enabled: true
        options:
          # 生成的文件后缀
          suffix: ".freezed.dart"
      
      # JSON序列化代码生成
      json_serializable:json_serializable:
        enabled: true
        options:
          # 生成的文件后缀
          suffix: ".g.dart"
          # 生成选项
          any_map: false
          checked: false
          create_factory: true
          create_to_json: true
          disallow_unrecognized_keys: false
          explicit_to_json: false
          field_rename: none
          generic_argument_factories: false
          ignore_unannotated: false
          include_if_null: true
