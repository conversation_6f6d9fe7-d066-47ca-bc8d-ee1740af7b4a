import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import '../../core/storage/storage_service.dart';

part 'app_state_provider.freezed.dart';
part 'app_state_provider.g.dart';

/// 应用状态
@freezed
class AppState with _$AppState {
  const factory AppState({
    @Default(false) bool isInitialized,
    @Default(true) bool isFirstLaunch,
    @Default(false) bool isPrivacyAccepted,
    @Default(false) bool isLoading,
    String? error,
  }) = _AppState;
}

/// 应用状态管理器
@riverpod
class AppStateNotifier extends _$AppStateNotifier {
  @override
  AppState build() {
    return const AppState();
  }
  
  /// 初始化应用状态
  Future<void> initialize() async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final storageService = ref.read(storageServiceProvider);
      await storageService.init();
      
      final isFirstLaunch = storageService.isFirstLaunch;
      final isPrivacyAccepted = storageService.isPrivacyAccepted;
      
      state = state.copyWith(
        isInitialized: true,
        isFirstLaunch: isFirstLaunch,
        isPrivacyAccepted: isPrivacyAccepted,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }
  
  /// 设置首次启动状态
  Future<void> setFirstLaunch(bool value) async {
    try {
      final storageService = ref.read(storageServiceProvider);
      await storageService.setFirstLaunch(value);
      state = state.copyWith(isFirstLaunch: value);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }
  
  /// 设置隐私协议同意状态
  Future<void> setPrivacyAccepted(bool value) async {
    try {
      final storageService = ref.read(storageServiceProvider);
      await storageService.setPrivacyAccepted(value);
      state = state.copyWith(isPrivacyAccepted: value);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }
  
  /// 清除错误状态
  void clearError() {
    state = state.copyWith(error: null);
  }
  
  /// 设置加载状态
  void setLoading(bool loading) {
    state = state.copyWith(isLoading: loading);
  }
}
