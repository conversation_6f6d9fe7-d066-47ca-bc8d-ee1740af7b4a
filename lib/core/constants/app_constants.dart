/// 应用常量定义
class AppConstants {
  // 应用信息
  static const String appName = 'Flutter Information Collection';
  static const String appVersion = '1.0.0';
  
  // 存储键名
  static const String keyIsFirstLaunch = 'is_first_launch';
  static const String keyPrivacyAccepted = 'privacy_accepted';
  
  // 路由路径
  static const String routeSplash = '/';
  static const String routePrivacy = '/privacy';
  static const String routeHome = '/home';
  
  // 网络配置
  static const String baseUrl = 'https://api.example.com';
  static const int connectTimeout = 30000;
  static const int receiveTimeout = 30000;
  static const int sendTimeout = 30000;
  
  // UI配置
  static const double defaultPadding = 16.0;
  static const double defaultRadius = 8.0;
  static const double defaultElevation = 2.0;
}
