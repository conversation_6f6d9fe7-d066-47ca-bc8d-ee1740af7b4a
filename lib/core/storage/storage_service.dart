import 'package:shared_preferences/shared_preferences.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../constants/app_constants.dart';

part 'storage_service.g.dart';

/// 本地存储服务
class StorageService {
  late final SharedPreferences _prefs;
  
  /// 初始化存储服务
  Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }
  
  /// 检查是否是首次启动
  bool get isFirstLaunch {
    return _prefs.getBool(AppConstants.keyIsFirstLaunch) ?? true;
  }
  
  /// 设置首次启动标记
  Future<bool> setFirstLaunch(bool value) {
    return _prefs.setBool(AppConstants.keyIsFirstLaunch, value);
  }
  
  /// 检查是否已同意隐私协议
  bool get isPrivacyAccepted {
    return _prefs.getBool(AppConstants.keyPrivacyAccepted) ?? false;
  }
  
  /// 设置隐私协议同意状态
  Future<bool> setPrivacyAccepted(bool value) {
    return _prefs.setBool(AppConstants.keyPrivacyAccepted, value);
  }
  
  /// 清除所有数据
  Future<bool> clear() {
    return _prefs.clear();
  }
  
  /// 移除指定键的数据
  Future<bool> remove(String key) {
    return _prefs.remove(key);
  }
  
  /// 获取字符串值
  String? getString(String key) {
    return _prefs.getString(key);
  }
  
  /// 设置字符串值
  Future<bool> setString(String key, String value) {
    return _prefs.setString(key, value);
  }
  
  /// 获取整数值
  int? getInt(String key) {
    return _prefs.getInt(key);
  }
  
  /// 设置整数值
  Future<bool> setInt(String key, int value) {
    return _prefs.setInt(key, value);
  }
  
  /// 获取布尔值
  bool? getBool(String key) {
    return _prefs.getBool(key);
  }
  
  /// 设置布尔值
  Future<bool> setBool(String key, bool value) {
    return _prefs.setBool(key, value);
  }
}

/// 存储服务提供者
@riverpod
StorageService storageService(StorageServiceRef ref) {
  return StorageService();
}
