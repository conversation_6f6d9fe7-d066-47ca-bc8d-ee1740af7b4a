import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../core/constants/app_constants.dart';
import '../features/splash/splash_page.dart';
import '../features/privacy/privacy_page.dart';
import '../features/home/<USER>';

part 'app_router.g.dart';

/// 路由配置
@riverpod
GoRouter appRouter(AppRouterRef ref) {
  return GoRouter(
    initialLocation: AppConstants.routeSplash,
    debugLogDiagnostics: true,
    routes: [
      // 启动页
      GoRoute(
        path: AppConstants.routeSplash,
        name: 'splash',
        builder: (context, state) => const SplashPage(),
      ),
      
      // 隐私协议页
      GoRoute(
        path: AppConstants.routePrivacy,
        name: 'privacy',
        builder: (context, state) => const PrivacyPage(),
      ),
      
      // 主页
      GoRoute(
        path: AppConstants.routeHome,
        name: 'home',
        builder: (context, state) => const HomePage(),
      ),
    ],
    
    // 错误页面
    errorBuilder: (context, state) => Scaffold(
      appBar: AppBar(
        title: const Text('页面未找到'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              '页面未找到: ${state.location}',
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go(AppConstants.routeSplash),
              child: const Text('返回首页'),
            ),
          ],
        ),
      ),
    ),
  );
}
