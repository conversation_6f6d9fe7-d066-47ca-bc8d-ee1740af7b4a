import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../core/constants/app_constants.dart';
import '../../shared/providers/app_state_provider.dart';

/// 启动页
class SplashPage extends ConsumerStatefulWidget {
  const SplashPage({super.key});

  @override
  ConsumerState<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends ConsumerState<SplashPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _initializeApp();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// 设置动画
  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeIn,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _animationController.forward();
  }

  /// 初始化应用
  Future<void> _initializeApp() async {
    // 初始化应用状态
    await ref.read(appStateNotifierProvider.notifier).initialize();
    
    // 等待动画完成
    await Future.delayed(const Duration(seconds: 3));
    
    if (!mounted) return;
    
    // 根据状态导航到相应页面
    _navigateToNextPage();
  }

  /// 导航到下一个页面
  void _navigateToNextPage() {
    final appState = ref.read(appStateNotifierProvider);
    
    if (appState.isFirstLaunch) {
      // 新用户，直接进入主页
      context.go(AppConstants.routeHome);
      // 标记为非首次启动
      ref.read(appStateNotifierProvider.notifier).setFirstLaunch(false);
    } else {
      // 老用户，进入隐私协议页面
      context.go(AppConstants.routePrivacy);
    }
  }

  @override
  Widget build(BuildContext context) {
    final appState = ref.watch(appStateNotifierProvider);
    
    return Scaffold(
      backgroundColor: Theme.of(context).primaryColor,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Logo动画
            AnimatedBuilder(
              animation: _animationController,
              builder: (context, child) {
                return FadeTransition(
                  opacity: _fadeAnimation,
                  child: ScaleTransition(
                    scale: _scaleAnimation,
                    child: Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(60),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.2),
                            blurRadius: 10,
                            offset: const Offset(0, 5),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.info_outline,
                        size: 60,
                        color: Colors.blue,
                      ),
                    ),
                  ),
                );
              },
            ),
            
            const SizedBox(height: 32),
            
            // 应用名称
            FadeTransition(
              opacity: _fadeAnimation,
              child: Text(
                AppConstants.appName,
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 版本号
            FadeTransition(
              opacity: _fadeAnimation,
              child: Text(
                'v${AppConstants.appVersion}',
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.white70,
                ),
              ),
            ),
            
            const SizedBox(height: 48),
            
            // 加载指示器
            if (appState.isLoading)
              const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            
            // 错误信息
            if (appState.error != null)
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  '初始化失败: ${appState.error}',
                  style: const TextStyle(
                    color: Colors.red,
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
