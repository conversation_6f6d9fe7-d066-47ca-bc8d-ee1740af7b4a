import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../core/constants/app_constants.dart';
import '../../shared/providers/app_state_provider.dart';

/// 隐私协议页面
class PrivacyPage extends ConsumerWidget {
  const PrivacyPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            children: [
              // 标题
              const SizedBox(height: 32),
              const Icon(
                Icons.privacy_tip_outlined,
                size: 64,
                color: Colors.blue,
              ),
              const SizedBox(height: 16),
              const Text(
                '隐私政策与用户协议',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              const Text(
                '请仔细阅读以下条款',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 32),
              
              // 协议内容
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: const SingleChildScrollView(
                    child: Text(
                      _privacyContent,
                      style: TextStyle(
                        fontSize: 14,
                        height: 1.5,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                ),
              ),
              
              const SizedBox(height: 24),
              
              // 按钮区域
              Row(
                children: [
                  // 不同意按钮
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => _handleDisagree(context),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        side: const BorderSide(color: Colors.red),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
                        ),
                      ),
                      child: const Text(
                        '不同意',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.red,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                  
                  const SizedBox(width: 16),
                  
                  // 同意按钮
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => _handleAgree(context, ref),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        elevation: AppConstants.defaultElevation,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
                        ),
                      ),
                      child: const Text(
                        '同意并继续',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 处理同意操作
  void _handleAgree(BuildContext context, WidgetRef ref) async {
    // 设置隐私协议已同意
    await ref.read(appStateNotifierProvider.notifier).setPrivacyAccepted(true);
    
    if (context.mounted) {
      // 进入主页
      context.go(AppConstants.routeHome);
    }
  }

  /// 处理不同意操作
  void _handleDisagree(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('确认退出'),
        content: const Text('不同意隐私政策将无法使用本应用，确定要退出吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              // 退出应用
              SystemNavigator.pop();
            },
            child: const Text(
              '确定退出',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }
}

/// 隐私协议内容
const String _privacyContent = '''
欢迎使用我们的应用！

为了更好地保护您的隐私权益，请您仔细阅读以下隐私政策：

1. 信息收集
我们可能会收集以下信息：
• 设备信息（设备型号、操作系统版本等）
• 使用情况数据（功能使用频率、错误日志等）
• 您主动提供的信息

2. 信息使用
我们收集的信息将用于：
• 提供和改进服务
• 分析用户行为，优化用户体验
• 技术支持和故障排除

3. 信息保护
我们承诺：
• 采用行业标准的安全措施保护您的信息
• 不会向第三方出售您的个人信息
• 仅在法律要求或您同意的情况下共享信息

4. 您的权利
您有权：
• 查看我们收集的关于您的信息
• 要求更正或删除您的个人信息
• 随时撤回对数据处理的同意

5. 联系我们
如有任何疑问，请通过以下方式联系我们：
邮箱：<EMAIL>
电话：400-123-4567

本政策最后更新时间：2024年1月1日

点击"同意并继续"即表示您已阅读并同意本隐私政策。
''';
