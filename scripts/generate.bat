@echo off
chcp 65001 >nul

REM Flutter代码生成脚本 (Windows版本)

echo 🚀 开始代码生成...

REM 检查Flutter是否安装
flutter --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Flutter未安装或未添加到PATH
    pause
    exit /b 1
)

REM 清理之前生成的文件
echo 🧹 清理之前生成的文件...
flutter packages pub run build_runner clean

REM 获取依赖
echo 📦 获取依赖包...
flutter pub get

REM 生成代码
echo ⚙️ 生成代码...
flutter packages pub run build_runner build --delete-conflicting-outputs

REM 检查生成结果
if %errorlevel% equ 0 (
    echo ✅ 代码生成完成！
    echo.
    echo 生成的文件包括：
    echo - *.g.dart ^(Riverpod Provider 和 JSON 序列化^)
    echo - *.freezed.dart ^(Freezed 数据类^)
    echo.
    echo 现在可以运行应用：
    echo flutter run
) else (
    echo ❌ 代码生成失败！
    pause
    exit /b 1
)

pause
