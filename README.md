# Flutter Information Collection

一个基于Flutter的信息收集应用，使用现代化的架构和最佳实践。

## 项目特性

- 🎯 **状态管理**: 使用 Riverpod 进行状态管理
- 🚀 **路由管理**: 使用 go_router 进行导航
- 🌐 **网络请求**: 使用 dio 进行HTTP请求
- 💾 **本地存储**: 使用 shared_preferences 进行数据持久化
- 🔧 **代码生成**: 使用 freezed 和 build_runner 生成样板代码
- 📱 **响应式设计**: 适配不同屏幕尺寸
- 🎨 **Material 3**: 使用最新的Material Design 3

## 项目结构

```
lib/
├── main.dart                    # 应用入口
├── app.dart                     # 应用根组件
├── core/                        # 核心功能
│   ├── constants/              # 常量定义
│   │   └── app_constants.dart
│   ├── storage/                # 本地存储
│   │   └── storage_service.dart
│   └── network/                # 网络请求
│       └── dio_client.dart
├── features/                   # 功能模块
│   ├── splash/                 # 启动页
│   │   └── splash_page.dart
│   ├── privacy/                # 隐私协议
│   │   └── privacy_page.dart
│   └── home/                   # 主页
│       └── home_page.dart
├── shared/                     # 共享组件
│   └── providers/              # 全局状态管理
│       └── app_state_provider.dart
└── router/                     # 路由配置
    └── app_router.dart
```

## 功能流程

1. **应用启动**: 显示启动页面，初始化应用状态
2. **用户检测**:
   - 新用户：直接进入主页面
   - 老用户：显示隐私协议弹窗
3. **隐私协议**:
   - 同意：进入主页面
   - 不同意：退出应用
4. **主页面**: 显示应用主要功能

## 依赖包

### 生产依赖
- `flutter_riverpod: 2.5.1` - 状态管理
- `riverpod_annotation: 2.3.5` - Riverpod注解
- `go_router: 14.2.7` - 路由管理
- `dio: 5.7.0` - 网络请求
- `freezed_annotation: 2.4.4` - 代码生成注解
- `json_annotation: 4.9.0` - JSON序列化注解
- `shared_preferences: 2.3.2` - 本地存储

### 开发依赖
- `build_runner: 2.4.12` - 代码生成工具
- `freezed: 2.5.7` - 不可变类生成
- `json_serializable: 6.8.0` - JSON序列化
- `riverpod_generator: 2.4.3` - Riverpod代码生成

## 开始使用

### 1. 安装依赖

```bash
flutter pub get
```

### 2. 生成代码

```bash
flutter packages pub run build_runner build
```

或者监听文件变化自动生成：

```bash
flutter packages pub run build_runner watch
```

### 3. 运行应用

```bash
flutter run
```

## 代码生成

项目使用了多个代码生成工具：

- **Riverpod Generator**: 自动生成Provider代码
- **Freezed**: 生成不可变数据类
- **JSON Serializable**: 生成JSON序列化代码

当修改了带有注解的文件后，需要运行代码生成命令来更新生成的文件。

## 架构说明

### 状态管理
使用 Riverpod 进行状态管理，提供了：
- 类型安全的依赖注入
- 自动资源管理
- 测试友好的架构

### 路由管理
使用 go_router 进行路由管理，支持：
- 声明式路由配置
- 类型安全的导航
- 深度链接支持

### 网络层
使用 dio 封装网络请求，提供了：
- 请求/响应拦截器
- 错误处理
- 日志记录

### 本地存储
使用 shared_preferences 进行本地数据存储，管理：
- 用户首次启动状态
- 隐私协议同意状态
- 其他应用配置

## 开发规范

1. **文件命名**: 使用小写字母和下划线
2. **类命名**: 使用大驼峰命名法
3. **变量命名**: 使用小驼峰命名法
4. **常量命名**: 使用大写字母和下划线
5. **代码格式化**: 使用 `flutter format` 格式化代码
6. **代码分析**: 使用 `flutter analyze` 检查代码质量

## 许可证

本项目采用 MIT 许可证。

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.
